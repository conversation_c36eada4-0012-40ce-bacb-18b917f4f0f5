<!DOCTYPE html>
<html class="no-js">

<head>
    <meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="robots" content="noai, noimageai">
<title> - Take the roads less travelled on our British Columbia fly-drive holiday with Bon Voyage</title>

<meta name="description" content=" - Take a walk on the wild side on the roads less travelled on this British Columbia fly-drive holiday with Bon Voyage, travel experts since 1979." /><link href="/favicon.ico" type="image/x-icon" rel="icon" /><link href="/favicon.ico" type="image/x-icon" rel="shortcut icon" />  <link rel="canonical" href="https://www.bon-voyage.co.uk/itineraries/british_columbia_backroads_and_beyond"/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="google-site-verification" content="8Qky044688YQ4V7s1Fuw2Jy3pBxLRoIQPv-0FJTEeXs" />
<script type='application/ld+json'>
{
  "@context": "http://www.schema.org",
  "@type": "LocalBusiness",
  "name": "Bon Voyage Travel & Tours ltd",
  "url": "https://www.bon-voyage.co.uk/",
  "telephone": "+44 (0) 2380 248248",
  "logo": "https://www.bon-voyage.co.uk/img/uploads/12810_fit588x588.jpg",
  "image": "https://lh3.googleusercontent.com/Mxw_em6V38diV85ZVnzoKfD19qDX-1IXbtz0sKv3eut8A6WE8cXJwxr3eDv8ZFP5-kPLfrHhTMF0=w1920-h1080-rw-no",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "16-18 Bellevue Road",
    "addressLocality": "Southampton",
    "addressRegion": "Hampshire",
    "postalCode": "SO15 2AY",
    "addressCountry": "United Kingdom"
  },
  "hasMap": "https://www.google.co.uk/maps/place/Bon+Voyage+Travel/@50.9124277,-1.4019383,15z/data=!4m5!3m4!1s0x0:0x6d1a3499fa1c9b4a!8m2!3d50.9124277!4d-1.4019383",
  "openingHours": "Mo, Tu, We, Th 09:00-18:00 Fr 09:00-17:30 Sa 09:00-15:00",
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "reservations",
    "telephone": "+44 (0) 2380 248248"
  }
}
 </script>
<script>window.GMapsKey = 'AIzaSyCNuTeVigyAWn-zYwCDourJfKS4kHVnu8I';</script><script>
    /*! loadJS: load a JS file asynchronously. [c]2014 @scottjehl, Filament Group, Inc. (Based on http://goo.gl/REQGQ by Paul Irish). Licensed MIT */
    function loadJS( src, cb ){
        "use strict";
        var ref = window.document.getElementsByTagName( "script" )[ 0 ];
        var script = window.document.createElement( "script" );
        script.src = src;
        script.async = true;
        ref.parentNode.insertBefore( script, ref );
        if (cb && typeof(cb) === "function") {
            script.onload = cb;
        }
        return script;
    }
</script>
<script>
    /*!
    loadCSS: load a CSS file asynchronously.
    [c]2014 @scottjehl, Filament Group, Inc.
    Licensed MIT
    */
    function loadCSS( href, before, media, callback ){
        "use strict";
        // Arguments explained:
        // `href` is the URL for your CSS file.
        // `before` optionally defines the element we'll use as a reference for injecting our <link>
        // By default, `before` uses the first <script> element in the page.
        // However, since the order in which stylesheets are referenced matters, you might need a more specific location in your document.
        // If so, pass a different reference element to the `before` argument and it'll insert before that instead
        // note: `insertBefore` is used instead of `appendChild`, for safety re: http://www.paulirish.com/2011/surefire-dom-element-insertion/
        var ss = window.document.createElement( "link" );
        var ref = before || window.document.getElementsByTagName( "script" )[ 0 ];
        var sheets = window.document.styleSheets;
        ss.rel = "stylesheet";
        ss.href = href;
        // temporarily, set media to something non-matching to ensure it'll fetch without blocking render
        ss.media = "only x";
        ss.onload = callback || function() {};
        // inject link
        ref.parentNode.insertBefore( ss, ref );
        // This function sets the link's media back to `all` so that the stylesheet applies once it loads
        // It is designed to poll until document.styleSheets includes the new sheet.
        function toggleMedia(){
            var defined;
            for( var i = 0; i < sheets.length; i++ ){
                if( sheets[ i ].href && sheets[ i ].href.indexOf( href ) > -1 ){
                    defined = true;
                }
            }
            if( defined ){
                ss.media = media || "all";
            }
            else {
                setTimeout( toggleMedia );
            }
        }
        toggleMedia();
        return ss;
    }
</script>
<link rel="stylesheet" href="https://assets.bon-voyage.co.uk/css/build/screen.1748856187.css">        <link rel="stylesheet" href="/css/navigation.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.css" />

    <script>
/**
 * Inline search toggle fix for the main site
 * This script runs immediately to fix the search toggle before any other scripts run
 */
(function() {
    // Set a flag to indicate this script has run
    window.searchToggleFixedInline = true;

    // Only run if our main script hasn't run yet
    if (!window.searchToggleFixedUniversal) {
        console.log('[Search Toggle Fix] Inline script initializing');

        // Function to fix the search toggle
        function fixSearchToggle() {
            // Find all search toggle buttons
            const searchToggles = document.querySelectorAll('.search-toggle');
            
            if (searchToggles.length > 0) {
                console.log('[Search Toggle Fix] Inline: Found ' + searchToggles.length + ' search toggle button(s)');
                
                // Fix each search toggle button
                searchToggles.forEach(function(searchToggle) {
                    // Skip if already fixed
                    if (searchToggle.hasAttribute('data-search-toggle-inline-fixed')) {
                        return;
                    }
                    
                    // Store the original data-toggle value if it exists
                    const originalToggleTarget = searchToggle.getAttribute('data-toggle');
                    
                    // CRITICAL: Remove the data-toggle attribute to prevent App.Navigation from handling it
                    if (searchToggle.hasAttribute('data-toggle')) {
                        console.log('[Search Toggle Fix] Inline: Removing data-toggle attribute');
                        searchToggle.removeAttribute('data-toggle');
                    }
                    
                    // Mark this button as fixed
                    searchToggle.setAttribute('data-search-toggle-inline-fixed', 'true');
                    
                    // Add our click event listener
                    searchToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Toggle active class on button
                        this.classList.toggle('active');
                        
                        // Try multiple ways to find the search element
                        let searchElement = null;
                        
                        // Method 1: Use the original data-toggle value
                        if (originalToggleTarget) {
                            searchElement = document.getElementById(originalToggleTarget) || 
                                           document.querySelector('.' + originalToggleTarget);
                        }
                        
                        // Method 2: Try to find by ID 'search'
                        if (!searchElement) {
                            searchElement = document.getElementById('search');
                        }
                        
                        // Method 3: Try to find by class
                        if (!searchElement) {
                            searchElement = document.querySelector('.primary-search');
                        }
                        
                        if (searchElement) {
                            // Toggle 'active' class on the search element
                            searchElement.classList.toggle('active');
                            console.log('[Search Toggle Fix] Inline: Toggled search element visibility');
                            
                            // For mobile, ensure proper display style
                            if (window.innerWidth < 1024) {
                                if (searchElement.classList.contains('active')) {
                                    searchElement.style.display = 'block';
                                } else {
                                    // Only hide on mobile
                                    searchElement.style.display = '';
                                }
                            }
                        } else {
                            console.error('[Search Toggle Fix] Inline: Search element not found');
                        }
                    });
                });
            } else {
                // Buttons not found yet, try again later
                console.log('[Search Toggle Fix] Inline: Buttons not found yet, will try again on DOMContentLoaded');
            }
        }
        
        // Try to run immediately
        fixSearchToggle();
        
        // Also run on DOMContentLoaded as a fallback
        document.addEventListener('DOMContentLoaded', function() {
            // Only run if not already fixed by our main script
            if (!window.searchToggleFixedUniversal) {
                console.log('[Search Toggle Fix] Inline: Running on DOMContentLoaded');
                fixSearchToggle();
            }
        });
    }
})();
</script>
    <link rel="stylesheet" href="/css/tweaks.css?v=5">

    <script>
    (function (c, s, q, u, a, r, e) {
        c.hj=c.hj||function(){(c.hj.q=c.hj.q||[]).push(arguments)};
        c._hjSettings = { hjid: a };
        r = s.getElementsByTagName('head')[0];
        e = s.createElement('script');
        e.async = true;
        e.src = q + c._hjSettings.hjid + u;
        r.appendChild(e);
    })(window, document, 'https://static.hj.contentsquare.net/c/csq-', '.js', 5238088);
    </script>

</head>

<body>
    <div class="page-wrapper 1 ">
        <header class="page-header">
    <div class="page-header__inner">
        <!-- <div class="desktop-nav-wrap"> -->
            <a class="page-header__logo" href="/">
                USA &amp; Canada Holidays - Bon Voyage
            </a>
            <button class="nav-toggle mmenu-trigger" aria-label="Toggle mobile menu" aria-expanded="false">
                <img src="/img/site/icons/hamburger.svg" alt="Menu" class="hamburger-icon">
                <img src="/img/site/icons/cross.svg" alt="Close" class="cross-icon" style="display: none;">
            </button>
            <!-- Mobile Menu -->
            <nav id="mobile-menu" role="navigation" aria-label="Mobile navigation">
                <ul><li class="first_child"><a href="/blog">Blog</a></li><li><a href="/faqs">FAQs</a></li><li class="last_child"><a href="/make_an_enquiry">Make an Enquiry</a></li></ul>            </nav>
            <button class="search-toggle" data-toggle="search"><img src="/img/site/icons/search.svg" alt="Search" class="active"></button>
            <!-- Desktop Menu -->
            <nav class="desktop-menu hidden-xs">
                <div class="desktop-menu__inner">
                    <!-- Primary Navigation -->
                    <nav class="primary-nav">
  <ul>
    <li><a href="/blog">Blog</a></li>
    <li><a href="/faqs">FAQs</a></li>
    <li><a href="/make_an_enquiry">Make an Enquiry</a></li>
  </ul>
</nav>
                    <!-- Secondary Navigation -->
                    <nav class="secondary-nav">
    <ul>
        <li>
            <a href="/destinations/usa_holidays" class="secondary-nav__dest" data-dropdown="usa-dropdown"><b>USA</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg></a>        </li>
        <li>
            <a href="/destinations/canada_holidays" class="secondary-nav__dest" data-dropdown="canada-dropdown"><b>Canada</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg></a>        </li>
        <li>
            <a href="/holidays" class="secondary-nav__dest" data-dropdown="holiday-types-dropdown"><b>Holiday Types</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg></a>        </li>
        <li>
            <a href="/whats_hot" class="secondary-nav__dest" data-dropdown="whats-hot-dropdown"><b>What&rsquo;s hot</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg></a>        </li>
        <li>
            <a href="/page/holiday_information" class="secondary-nav__dest" data-dropdown="holiday-info-dropdown"><b>Holiday Info</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg></a>        </li>
        <li>
            <a href="/page/about_bon_voyage" class="secondary-nav__dest" data-dropdown="about-dropdown"><b>About Us</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg></a>        </li>
    </ul>
</nav>
                </div>
            </nav>
        <!-- </div> -->
        <div class="primary-search primary-search--page-header" id="search">
            <form class="primary-search__form" action="/search" method="get" id="header-search-form">
                <fieldset>

                    <div class="primary-search__input">
                        <input type="text" name="search" id="header-search-input" placeholder="Search">
                        <div class="button-wrap"><button type="submit">Submit</button></div>

                    </div>


                </fieldset>
            </form>

            <script>
            (function() {
                // Immediately capture the header search form to prevent other scripts from interfering
                var form = document.getElementById('header-search-form');
                var searchInput = document.getElementById('header-search-input');

                if (form && searchInput) {
                    // Remove any existing event listeners by cloning the form
                    var newForm = form.cloneNode(true);
                    form.parentNode.replaceChild(newForm, form);

                    // Get references to the new elements
                    var newSearchInput = document.getElementById('header-search-input');

                    // Add our event listener with high priority
                    newForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        e.stopImmediatePropagation();

                        var searchTerm = newSearchInput.value.trim();
                        if (searchTerm) {
                            // Use + for spaces instead of %20 to avoid 403 errors
                            var encodedTerm = encodeURIComponent(searchTerm).replace(/%20/g, '+');
                            window.location.href = '/search/' + encodedTerm;
                        }
                        return false;
                    }, true); // Use capture phase for higher priority

                    console.log('[Header Search Form] Clean URL redirect handler installed');
                }
            })();
            </script>
        </div>
        <!-- Mega Menu Dropdowns -->
        <div class="mega-menu">
            <!-- USA Dropdown -->
            <div class="mega-menu__panel" id="usa-dropdown">
                <div class="mega-menu__inner">
                                    </div>
            </div>
            <!-- Canada Dropdown -->
            <div class="mega-menu__panel" id="canada-dropdown">
                <div class="mega-menu__inner">
                                    </div>
            </div>

            <!-- Holiday Types Dropdown -->
            <div class="mega-menu__panel" id="holiday-types-dropdown">
                <div class="mega-menu__inner">
                                    </div>
            </div>

            <!-- What's Hot Dropdown -->
            <div class="mega-menu__panel" id="whats-hot-dropdown">
                <div class="mega-menu__inner">
                                    </div>
            </div>
            <!-- Holiday Information Dropdown -->
            <div class="mega-menu__panel" id="holiday-info-dropdown">
                <div class="mega-menu__inner">
                                    </div>
            </div>
            <!-- About Dropdown -->
            <div class="mega-menu__panel" id="about-dropdown">
                <div class="mega-menu__inner">
                                    </div>
            </div>
        <!-- Mobile Search -->
            <!-- <div class="mobile-search">
            <form class="primary-search" action="/search" method="get">
                <input type="text" name="q" placeholder="Search...">
                <input type="submit" value="Search">
            </form>
                </div> -->

        <!-- Desktop Search -->
            <!-- <div class="desktop-search">
            <form class="primary-search" action="/search" method="get">
                <input type="text" name="q" placeholder="Search...">
                <input type="submit" value="Search">
            </form>
                </div> -->
</header>


<!-- <pre>
Holiday Types:
</pre> -->



        <section class="hero hero--banner ">
    <div class="hero__images">
                <div class="hero__image" style="background-image: url(https://resources.bon-voyage.co.uk/img/uploads/13133_crop1600x246.jpg);"></div>
    </div>
</section>

          <div class="breadcrumb">
    <div class="breadcrumb__inner">
      <ul>
        <li><a href="/">Home</a></li>

        <li><a href="/destinations/north_america">North America</a></li><li><a href="/destinations/canada_holidays">Canada Holidays</a></li><li>British Columbia Holidays</li>      </ul>
    </div>
  </div>

        <header class="page-content-header ">
    <h1 class="page-content-header__header js-first-word-bold">British Columbia Holidays</h1>

    <div class="page-content-header__feefo">
        <div class="feefo">
  <a class="feefo_score" href="http://www.feefo.com/GB/en/reviews/Bon-Voyage/?id=730331&amp;mode=service" title="Bon Voyage Reviews | Customer Reviews Of www.bon-voyage.co.uk">
    <img src="/proxy/feefo/?resource=feefo%2Ffeefologo.jsp%3Flogon%3Dwww.bon-voyage.co.uk%26amp%3Btemplate%3Dn150x45.png" alt="Feefo Reviews" />
  </a>

  <a class="feefo__all" href="http://www.feefo.com/GB/en/reviews/Bon-Voyage/?id=730331&amp;mode=service">Read all reviews</a>

  <div class="feefo-microdata" itemscope itemtype="http://schema.org/LocalBusiness">
  <span itemprop="name">
      </span>
  <span itemprop="logo">https://www.bon-voyage.co.uk/img/site/content/logos/bon-voyage.png</span>
  <span itemprop="image">https://lh3.googleusercontent.com/Mxw_em6V38diV85ZVnzoKfD19qDX-1IXbtz0sKv3eut8A6WE8cXJwxr3eDv8ZFP5-kPLfrHhTMF0=w1920-h1080-rw-no</span>
  <span itemprop="url">https://www.bon-voyage.co.uk</span>
  <span itemprop="telephone">+44 (0) 2380 248248</span>
  <div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
      <span itemprop="streetAddress">16-18 Bellevue Road</span>
      <span itemprop="addressLocality">Southampton</span>,
      <span itemprop="addressRegion">Hampshire</span>
      <span itemprop="addressCountry">GB</span>
      <span itemprop="postalCode">SO15 2AY</span>
  </div>
  <div itemprop="aggregateRating" itemscope itemtype="http://schema.org/AggregateRating">
    <p itemprop="worstRating">
      0
    </p>
    <p itemprop="bestRating">
      100
    </p>
    <p itemprop="ratingValue">
          </p>
    <p itemprop="ratingCount">
          </p>
  </div>
</div>
</div>
    </div>
</header>

<section class="page-content-body js-page-content-body page-content-body--destination">


<nav class="destinations-nav">
  <div class="destinations-nav__inner">
    <ul class="js-related-tabs">
      <li >
        <button data-target="overviewContent">Overview</button>
      </li>

                                                                                        
          </ul>
  </div>
</nav>


        <div class="image-and-map__map-wrapper image-and-map__map-wrapper--fixed">
        <div class="image-and-map__map-wrapper__map ui-map-editor" id="image-and-map__map"></div>
        <button class="image-and-map__hide" type="button">Close</button>
        <noscript>
            <img src="//maps.googleapis.com/maps/api/staticmap?center=52.049583,-126.207977&amp;zoom=5&amp;size=355x340&amp;maptype=terrain" width="355" height="340" alt="" />        </noscript>
    </div>
    
    <div class="page-content-body__inner">
        <div class="page-left-sidebar  js-page-left-sidebar">
    
  <nav class="sidebar-nav sidebar-nav--destinations">
    <div class="sidebar-nav__inner">
      <ul><li class="first_child last_child"><ul><li class="first_child last_child"><ul><li class="selected first_child last_child"><ul><li class="first_child"><a href="/destinations/vancouver_holidays">Vancouver Holidays</a></li><li><a href="/destinations/vancouver_island_holidays">Vancouver Island Holidays</a></li><li><a href="/destinations/whistler">Whistler Holidays</a></li><li><a href="/destinations/the_kootenay_rockies">The Kootenay Rockies</a></li><li><a href="/destinations/northern_british_columbia">Northern British Columbia</a></li><li><a href="/destinations/the_thompson_okanagan">The Thompson Okanagan</a></li><li><a href="/destinations/vancouver_coast_and_mountains">Vancouver Coast and Mountains</a></li><li><a href="/destinations/the_cariboo_chilcotin_coast">The Cariboo Chilcotin Coast</a></li><li class="last_child"><a href="/destinations/wilderness_wildlife_experiences">Wilderness, Wildlife & Ranch Experiences </a></li></ul></li></ul></li></ul></li></ul>    </div>
  </nav>
</div>
    </div>


    <div class="page-content-body__inner">
        <div class="page-content-body__content js-related-content">

            
<header class="sub-section-header">
    <h2>British Columbia -  Off the Beaten Track</h2>

    <p class="sub-section-header__price">
        From: <b>&pound;4275</b>
    </p>
</header>


<div class="content-blocks content-blocks--itinerary">
  <div class="content-block content-block--no-image content-block--right"><div class="content-block__image content-block__image--right"><a href="https://resources.bon-voyage.co.uk/img/uploads/4498_fit588x588.jpg" class="lightbox" title="Quesnel Lake"><img src="https://resources.bon-voyage.co.uk/img/uploads/4498_crop370x370.jpg" alt="Quesnel Lake" srcset="" /></a></div><div class="content-block__text"><p>If you're looking for a fly-drive that takes you off the beaten track to parts of Canada that are sparsely populated, wonderfully scenic and abundant with wildlife - you've found it.&nbsp;</p><p>This BC adventure takes you through the historic gold mining towns of years gone by, into the vast and untouched grizzly bear wilderness of Northern British Columbia before you board the ferry for one of the most scenic coastal trips in North America - <i>The Inside Passage</i> to Vancouver Island.</p><p>You've plenty of time once in Vancouver Island to enjoy bracing strolls along it's wild beaches; discover its magnificent rainforests and experience its famed whale-watching trips.&nbsp; &nbsp;</p><p>Price per person includes:<br></p><ul><li>Flights from the UK to Vancouver</li><li>Car hire</li><li>16 nights accommodation in resorts, lodges, inns or B&amp;Bs</li><li>Inside Passage BC Ferry with day cabin from Prince Rupert to Port Hardy</li><li>Ferry from Nanaimo to Horseshoe Bay&nbsp;</li></ul><p>All Bon Voyage holidays are tailor-made to your exact requirements. Please call the team to discuss your off the beaten path holiday.&nbsp;&nbsp;</p> <p> To order a FREE map of British Columbia click  <a href="/cdn-cgi/l/email-protection#47242629262326072528296a31283e26202269242869322c781432252d2224337a172b22263422627577372834336275772a3e62757705046275772a263762757733286969696969690e6275702a6275772f28372e2920627577332862757733352631222b6275772e296275776969696969"> here&nbsp;</a></p></div></div></div>
<aside class="page-right-sidebar page-right-sidebar--destination">
  </aside>

<div class="itinerary-cta"><a href="/make_an_enquiry?message=Trip%3A+British+Columbia+++Off+the+Beaten+Track" class="cover"></a>
            <span class="itinerary-cta-inner"><img src="/img/site/icons/call.svg" class="card__icon" alt=""><span class="self-left"><strong>Keen to know more?</strong>  Schedule a call with our friendly sales experts today!</span> </span>
            <span class="button button--with-arrow button--block">Get in touch <img class="" src="/img/site/icons/chevron_right-fff.svg" alt=""></span>
        </div>

<div class="content-blocks content-blocks--itinerary-day">

            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 1   : Vancouver                </h3>

                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/15252_fit588x588.jpg" class="lightbox" title="Vancouver aerial Credit Destination BC Thomas Hill"><img src="https://resources.bon-voyage.co.uk/img/uploads/15252_crop370x370.jpg" alt="Vancouver aerial Credit Destination BC Thomas Hill" srcset="" /></a></div>                </div>

                <p>Arrive into Vancouver early evening in time for a stroll around the harbour front, or dinner on Granville Island.</p><p></p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 2 : 70 Mile House                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 249 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/13314_fit588x588.jpg" class="lightbox" title="BC-Hat-Creek-Ranch"><img src="https://resources.bon-voyage.co.uk/img/uploads/13314_crop370x370.jpg" alt="BC-Hat-Creek-Ranch" srcset="" /></a></div>                </div>

                <p>Leave the cosmopolitan city of Vancouver behind and travel along scenic Highway 1 through tiny towns such as Boston Bar, and Cache Creek en route to your overnight stay in 70 Mile House. This is Gold Country with a rich gold mining history, abundant wildlife, lakes and rivers and fascinating historic towns.&nbsp;</p><p>Experience life in the days of the Gold Rush at historic Hat Creek Ranch. Here you can stroll along the same dirt road that the miners and their wagon trains travelled on in the 1860's as costumed interpreters dressed in period clothing, will take you on a guided tour of a bygone era.&nbsp;&nbsp;</p><p>70 Mile House is one of many towns established along the historic Cariboo Wagon Road route to the Cariboo goldfields. The town began as a mere roadhouse until some of the travellers passing through the junction decided to stay and put down roots.&nbsp;<br>&nbsp; &nbsp;</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 3 : Prince George, Northern British Columbia                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 230 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/13210_fit588x588.jpg" class="lightbox" title="BC Cariboo Chilcotin lake deck"><img src="https://resources.bon-voyage.co.uk/img/uploads/13210_crop370x370.jpg" alt="BC Cariboo Chilcotin lake deck" srcset="" /></a></div>                </div>

                <p>Today you'll embark on the historic Cariboo Highway north from Cache Creek to Prince George in the shadow of the mighty Cariboo Mountains.</p><p>There's many wonderfully remote and peaceful stops along this route such as Lac La Hache, a small friendly community on the edge of the lake that spans over 15 miles. Around Williams Lake you'll view endless rolling ranch lands and giant cedar forests. Whilst, picture perfect Quesnel is located at the confluence of the Fraser and Quesnel Rivers  in a serene valley surrounded by beautiful mountains and lush forests.&nbsp;&nbsp;</p><p>Make the day of your drive today and plan to arrive in Prince George early evening to catch a bite to eat and live music in Nancy O's, or enjoy a spot of nostalgia at the Park Drive-In Movie Theatre.</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 4  : Smithers, Northern British Columbia                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 232 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/15508_fit588x588.jpg" class="lightbox" title="BC Fish Creek Smithers Photo Credit Destination BC Grant Harder"><img src="https://resources.bon-voyage.co.uk/img/uploads/15508_crop370x370.jpg" alt="BC Fish Creek Smithers Photo Credit Destination BC Grant Harder" srcset="" /></a></div>                </div>

                <p>The mountain town of Smithers is home to the Witsuwit’en people and its red-bricked Main Street is graced by alpine-themed architecture and murals celebrating the Bulkley Valley landscape, and crowned by the monumental presence of Hudson Bay Mountain.</p><p>Smithers ticks all the boxes with seven incredible mountain ranges surrounding the town, numerous coffeehouses and bistros, newly established craft breweries and a lively music scene.&nbsp;</p><p></p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Days 5 -6 : Terrace, Northern British Columbia                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 126 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/12721_fit588x588.jpg" class="lightbox" title="BC Totem poles"><img src="https://resources.bon-voyage.co.uk/img/uploads/12721_crop370x370.jpg" alt="BC Totem poles" srcset="" /></a></div>                </div>

                <p>As you travel through the Bulkley Valley take in the spectacular Coastal Mountain views and rolling farmland.  Visit Ksan Historical Village along the way in Hazelton for a taste of the lifestyle of the Ksan people before following the Skeena River for the rest of the drive.  The river played a vital role as a trading route for the Indigenous peoples as well as pioneers and explorers who used sternwheelers on the waterways.&nbsp; Take time to visit the Nisga'a Memorial Lava Bed Provincial Park, which at less than 300 years old is believed to be Canada’s most recent volcanic eruption and lava flow.Stop at the Nisga’a Heritage Museum to view Northwest Coast aboriginal art or take a self-driven tour of the Nass Valley to visit culturally significant sites such as hot springs, waterfalls and walkways through the lava beds.</p><p>Arrive in Terrace, beautifully situated in the Skeena River Valley amidst the Coast mountain Range, a proper city in the forest aptly named for the terraces carved into the landscape over centuries by the river.&nbsp; Sit back, relax and breathe in that clean mountain air!&nbsp;&nbsp;</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Days 7 - 8 : Prince Rupert, Northern British Columbia                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 90 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/15254_fit588x588.jpg" class="lightbox" title="BC Wildlife Bear Destination BC Blake Jorgenson2"><img src="https://resources.bon-voyage.co.uk/img/uploads/15254_crop370x370.jpg" alt="BC Wildlife Bear Destination BC Blake Jorgenson2" srcset="" /></a></div>                </div>

                <p>Prince Rupert is a traditional, mural-adorned coastal community on the northwest coast, surrounded by deep valleys, awesome mountain ranges and waterways.  Whilst only a small town it is one of the main points to access Northern BC, it is also the shortest link between North America and Asia and a mid-point on the famed Inside Passage, a sheltered waterway running between Vancouver and Alaska. &nbsp;</p><p>The drive itself into the town is said to be one of the most scenic drives in the province.  Based on the water, Prince Rupert offers many activities  focused around the ocean such as whale watching with the chance to spot Orca, Minke and Humpback whales often found in the waters offshore. Another worthy consideration - a visit to the Grizzly Bear Sanctuary, home to the highest concentration of bears in the province.  It’s an early morning, but worth it to witness the bears foraging along the shoreline by boat.&nbsp;</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 9 : Port Hardy via The Inside Passage                </h3>

                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/14256_fit588x588.jpg" class="lightbox" title="BC Pod of ocra whales"><img src="https://resources.bon-voyage.co.uk/img/uploads/14256_crop370x370.jpg" alt="BC Pod of ocra whales" srcset="" /></a></div>                </div>

                <p>Ferry along the Inside Passage on one of the most spectacular routes along the Pacific Coast. Keep your eyes peeled for wildlife as there are frequent sightings of whales, sea lions and bald eagles against a backdrop of stunning fjord-like inlets.&nbsp;</p><p></p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Days 10 - 11 : Campbell River, Vancouver Island                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 146 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/15506_fit588x588.jpg" class="lightbox" title="BC Elk Falls Provincial Park"><img src="https://resources.bon-voyage.co.uk/img/uploads/15506_crop370x370.jpg" alt="BC Elk Falls Provincial Park" srcset="" /></a></div>                </div>

                <p>Continue your adventure along the eastern coast of Vancouver Island, North America’s largest island -&nbsp; roughly the size of Ireland. It offers a taster of the wider-province’s features – rain forest, mountains and spectacular waterfront towns.    </p><p></p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Days 12 - 14 : Tofino/Ucluelet , Vancouver Island                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 169 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/15424_fit588x588.jpg" class="lightbox" title="Tonquin Park Tofino BC"><img src="https://resources.bon-voyage.co.uk/img/uploads/15424_crop370x370.jpg" alt="Tonquin Park Tofino BC" srcset="" /></a></div>                </div>

                <p>Journey through the heart of Vancouver Island to your coastal home for the next three nights, Tofino.  Travel from the sheltered east coast to the rugged west. En route, explore the ethereal MacMillan Provincial Park home to Cathedral Grove, a rare collection of ancient Douglas fir trees some of which are around 800 years old and have grown to 250ft in height and 29ft in circumference.  Once you reach the coast you will be in one of BC’s seven national parks, Pacific Rim National Park home to a spectacular coastline and temperate rainforest. </p><p>Tofino sits on a prime oceanfront peninsula, within the UNESCO Clayoqout Sound Biosphere Reserve, recognised for its incredibly diverse natural habitat and environmental culture. With the diverse ecosystem comes a whole host of wildlife excursions which can be pre-booked for you..</p><p></p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 15 : Parksville, Vancouver Island                </h3>

                                    <p class="content-block__text__miles">
                        Distance: 106 miles
                    </p>
                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/13316_fit588x588.jpg" class="lightbox" title="BC Rathtrevor-Beach"><img src="https://resources.bon-voyage.co.uk/img/uploads/13316_crop370x370.jpg" alt="BC Rathtrevor-Beach" srcset="" /></a></div>                </div>

                <p>Head back across the island to the east coast to spend the night in a relaxing beach resort hotel in the Parksville/Qualicum Beach area before boarding the ferry the following morning for the 2-hour scenic ride to Horseshoe Bay. </p> <p> Just before your arrival into Parksville, stop at the small community of Coombs - its Old Country Market is a great stop for souvenirs, lunch and you might even spot one of the famous goats on the roof.  If you fancy stretching your legs, take a wander on nearby Rathtrevor Beach or Qualicum Beach.&nbsp;</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 16 : Vancouver                </h3>

                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/3475_fit588x588.jpg" class="lightbox" title="Vancouver"><img src="https://resources.bon-voyage.co.uk/img/uploads/3475_crop370x370.jpg" alt="Vancouver" srcset="" /></a></div>                </div>

                <p>Surrounded by the Coastal Mountain range, cosmopolitan Vancouver is a joy to explore. We recommended to make the most of your time here jump aboard the excellent hop-on, hop-off bus sightseeing tours that operate reguarly through the city and its vibrant neighbourhoods.&nbsp; &nbsp;</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
            <div class="content-block js-itinerary-day">
            <div class="content-block__text js-itinerary-day-text">
                <h3>
                    Day 17 : Vancouver - Depart for the UK                </h3>

                
                <div class="content-block__image content-block__image--left">
                    <div class=""><a href="https://resources.bon-voyage.co.uk/img/uploads/13325_fit588x588.jpg" class="lightbox" title="BC Knight inlet bears"><img src="https://resources.bon-voyage.co.uk/img/uploads/13325_crop370x370.jpg" alt="BC Knight inlet bears" srcset="" /></a></div>                </div>

                <p>Some time for last minute sightseeing before your early evening flight back to the UK.</p><p>Want to extend your time in British Columbia? Perhaps, longer in Vancouver? Time on a ranch in the province's interior? Maybe a trip on the legendary Rocky Mountaineer? All Bon Voyage holidays can be tailor-made to your exact requirements.&nbsp;</p><p>Please call our friendly team of experts for further information on (0800) 316 3012.</p>            </div>
            <button class="content-block__more js-itinerary-day-btn" type="button" name="button">Read more...</button>
        </div>
    
    <p class="content-block__link content-block__link--back">
        <a href="/destinations/british_columbia_holidays/itineraries">BACK TO INDEX</a>    </p>
</div>


<div class="itinerary-cta"><a href="/make_an_enquiry?message=Trip%3A+British+Columbia+++Off+the+Beaten+Track" class="cover"></a>
            <span class="itinerary-cta-inner"><img src="/img/site/icons/call.svg" class="card__icon" alt=""><span class="self-left"><strong>Keen to know more?</strong>  Schedule a call with our friendly sales experts today!</span> </span>
            <span class="button button--with-arrow button--block">Get in touch <img class="" src="/img/site/icons/chevron_right-fff.svg" alt=""></span>
        </div>

</div>
</div>
<div class="usp-footer">
    <h2><b>The</b> Bon Voyage Holiday Promise</h2>
    <div class="usp-footer-row">
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/service.svg" alt="Hand holding Love Heart">
                <h3>Service</h3>
            </div>
            <p>Our friendly team of experts are here to help before, during and after your holiday</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/security.svg" alt="Shield with Check Mark">
                <h3>Security</h3>
            </div>
            <p>Your money and holiday are safe - we&rsquo;re ATOL bonded and members of ABTA</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/experience.svg" alt="Passport and Flight Tickets">
                <h3>Experience</h3>
            </div>
            <p>US and Canada specialists since 1979 - over 1,000 Transatlantic trips of our own</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/price.svg" alt="Padlock with Dollar Sign">
                <h3>Price</h3>
            </div>
            <p>No surcharges guaranteed and a Flexible Payment Plan to spread the cost</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/affordability.svg" alt="Piggy Bank">
                <h3>Afford&shy;ability</h3>
            </div>
            <p>Unrivalled supplier relationships mean optimum value for your money</p>
        </div>
        <div class="usp-col">
            <div class="usp-title"><img src="/img/site/icons/promise/bv_app.svg" alt="Mobile Phone App">
                <h3>BV App</h3>
            </div>
            <p>Your Bon Voyage holiday in your pocket – tickets, updates, landmarks and more</p>
        </div>
    </div>
</div>

<style>
.usp-footer {
    text-align: center;
}

.usp-footer h2 {
    font-size: 25px;
    line-height: 28px;
    color:#6C4000;
    font-weight: 300;
    text-transform: uppercase;
}
.usp-footer-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap; /* Ensure wrapping for all screen sizes */
    gap: 25px;
    max-width:1280px;
    padding: 10px 18px 40px;
    margin: 0 auto;
}
.usp-col {
    text-align: left;
    flex: 0 1 calc(50% - 12.5px); /* Default for narrow screens (two columns) */
    max-width: calc(50% - 12.5px);
    box-sizing: border-box;
}
.usp-title {
    display: flex;
    align-items: center;
    gap: 10px;
    hyphens: auto;
}
.usp-title h3 {
    margin: 0;
    font-size: 20px;
    line-height: 24px;
    color: #6C4000;
    font-weight: 400;
}
.usp-title img {
    width: 60px;
    height: 60px;
}
.usp-col p {
    font-size: 14px;
    line-height: 21px;
    color: #555;
}

/* Small screens (up to 550px) - 2 columns, 3 rows */
@media screen and (max-width: 550px) {
    .usp-col {
        flex: 0 1 100%; /* Each item is 100% width to create a single column */
        max-width: 100%;
    }
    .usp-title {
        gap: 15px;
    }
    .usp-title img {
        width: 40px;
        height: 40px;
    }
    .usp-title h3 {
        font-size: 22px;
        line-height: 28px;
        font-weight: 300;
    }
    .usp-col p {
        font-size: 15px;
        line-height: 22px;
        margin: 8px 0 5px 0;
    }
}

/* Small screens (up to 767px) - 2 columns, 3 rows */
@media screen and (min-width: 551px) and (max-width: 767px) {
    .usp-col {
        flex: 0 1 calc(50% - 12.5px); /* Each item is 50% width to create 2 columns */
        max-width: calc(50% - 12.5px);
    }
    .usp-title {
        gap: 15px;
    }
    .usp-title img {
        width: 80px;
        height: 80px;
    }
    .usp-title h3 {
        font-size: 25px;
        line-height: 30px;
        font-weight: 300;
    }
    .usp-col p {
        font-size: 15px;
        line-height: 22px;
    }
}

/* 2 rows of 3 columns for screens between 768px and 1000px */
@media screen and (min-width: 768px) and (max-width: 1000px) {
    .usp-col {
        flex: 0 1 calc(33.33% - 16.67px); /* Each item is one-third width, minus half the gap */
        max-width: calc(33.33% - 16.67px);
    }
}

/* 6 columns for screens wider than 1000px */
@media (min-width: 1000px) {
    .usp-col {
        flex: 0 1 calc(16.66% - 20.83px); /* Each item is one-sixth, minus half the 25px gap */
        max-width: calc(16.66% - 20.83px);
    }
}
</style>

<div class="testimonial-extract testimonial-extract--strip testimonial-extract--home" itemscope itemtype="http://schema.org/Review">
    <div class="testimonial-extract__inner">
        <iframe class="feefo-widget js-feefo-widget" src="/feefo-iframe.html" frameborder="0" height="200" width="100%" scrolling="no"></iframe>
    </div>
</div>
</section>


        <div class="page-footer">
    <div class="page-footer__inner">
        <div class="page-footer__about 1">
            <div class="page-footer__holidays">
    <div class="page-footer__holidays-inner">
        <h5>Our most popular USA and Canada holidays</h5>
        <ul>
            <li><a href="/holidays/multi_centre_combination_holidays/" title="Multi Centre Holidays USA | Twin Centre Holidays USA 2014-2015">Multi-Centre Holidays</a> </li>
            <li><a href="/holidays/fly_drive_holidays/" title="Fly Drive USA | Fly Drive America Holidays 2014-2015 :: Bon Voyage">Fly-Drive Holidays</a></li>
            <li><a href="/holidays/route_66_holidays/" title="Route 66 Holidays 2014-2015 :: Bon Voyage">Route 66 Holidays</a></li>
            <li><a href="/page/escorted_tours_and_learning_experiences/" title="Tauck Escorted Tours &amp; Globus Escorted Coach Tours of USA &amp; Canada :: Bon Voyage">Escorted Holidays</a></li>
            <li><a href="/holidays/harley_holidays_to_america/" title="Escorted and Independent Harley Holidays to America">Harley Holidays to America</a></li>
            <li><a href="/holidays/rail_holidays/" title="America Rail Holidays, Canada Rail Holidays - Great holidays without the driving!">USA &amp; Canada Rail Holidays</a></li>
            <li><a href="/holidays/honeymoons_and_celebrations/" title="Honeymoons in America | USA Honeymoons 2014/2015 :: Bon Voyage">Honeymoons</a></li>
            <li><a href="/holidays/motorhome_adventures/" title="Motorhome Holidays USA | RV Holidays USA 2014/2015 :: Bon Voyage">USA & Canada Motorhomes</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/california_holidays/" title="California Holidays | Holidays to California 2014-2015 :: Bon Voyage">California Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/hawaii_holidays/" title="Hawaii Holidays | Holidays to Hawaii 2013-2014 :: Bon Voyage">Hawaii Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/las_vegas_holidays/" title="Las Vegas Holidays 2014-2015 | Holidays to Las Vegas :: Bon Voyage">Las Vegas Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/new_york_holidays/" title="New York Holidays 2014-2015 | Holidays to New York :: Bon Voyage">New York Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/deep_south_holidays/" title="Deep South Tours | Deep South Holidays 2014/2015 :: Bon Voyage">Deep South Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/new_england_holidays/" title="New England Holidays | Holidays in New England 2013-2014 :: Bon Voyage">New England Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/florida_holidays/" title="Florida Holidays 2014/2015 | Holidays to Florida :: Bon Voyage">Florida Holidays</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/alberta_holidays/" title="Alberta">Alberta &amp; the Canadian Rockies</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/quebec_holidays/" title="Quebec Holidays">Quebec &amp; Eastern Canada</a></li>
            <li><a href="https://www.bon-voyage.co.uk/destinations/british_columbia_holidays/" title="British Columbia Holidays. Book your Holidays to British Columbia with Bon Voyage.">Vancouver &amp; British Columbia</a></li>
            <li><a href="https://www.bon-voyage.co.uk/itineraries/trans_canada_by_rail/" title="Trans Canada by Rail">Rocky Mountaineer across Canada</a></li>
        </ul>
    </div>
</div>

            <div class="page-footer__thirdp">
                <ul class="bv-community">
    <li>
        <a class="bv-community__facebook" href="http://www.facebook.com/BonVoyageUK">Bon Voyage USA Holidays on Facebook</a>
    </li>
    <li>
        <a class="bv-community__instagram" href="https://www.instagram.com/bonvoyagetraveluk/">Bon Voyage USA Holidays on Instagram</a>
    </li>
    <li>
        <a class="bv-community__linkedin" href="https://www.linkedin.com/company/22258884/">Bon Voyage USA Holidays on LinkedIn</a>
    </li>
    <li>
        <a class="bv-community__twitter" href="https://www.twitter.com/usaexpert">USA Holiday Expert on Twitter</a>
    </li>
    <li>
        <a class="bv-community__youtube" href="https://www.youtube.com/bonvoyagetravel">Bon Voyage YouTube Channel</a>
    </li>
    <li>
        <a class="bv-community__wordpress" href="https://www.bon-voyage.co.uk/blog/">Bon Voyage USA Blog</a>
    </li>
</ul>            </div>


            <div class="thirdp-associations">
  <a class="thirdp-associations__abta" href="http://abta.com/go-travel/before-you-travel/find-a-member/results?term=17213&amp;location=&amp;miles=&amp;limit=10#searchform">ABTA</a>
  <a class="thirdp-associations__iata" href="http://www.iata.org/">IATA</a>
  <a class="thirdp-associations__atol" href="http://www.atol.org.uk">ATOL</a>
  <a class="thirdp-associations__discover-america" href="https://www.visittheusa.co.uk/"><img src="/img/site/sprites/logos/USA-UK_su_vert_url_p2_4C.png" alt=""></a>
</div>
        </div>


        <div class="page-footer__nav">
            <nav class="footer-nav">
  <ul>
    <li><a href="/contact_us">Contact us</a></li>
    <li><a href="/page/careers">Careers</a></li>
    <li><a href="/page/press_centre">Press Centre</a></li>
    <li><a href="/page/privacy_policy">Privacy policy</a></li>
    <li><a href="/page/terms_conditions">Terms and conditions</a></li>
  </ul>
</nav>

            <div class="page-footer__copyright">
                <div itemscope itemtype="http://schema.org/LocalBusiness">
                    <div itemprop="image" itemscope itemtype="http://schema.org/ImageObject" style="display: none;">
                        <img src="https://www.bon-voyage.co.uk/img/uploads/12810_fit588x588.jpg" itemprop="url">
                    </div>
                    <div>&copy; 2025 <span itemprop="name">Bon Voyage Travel &amp; Tours Ltd</span></div>
                    <div>Phone: <span itemprop="telephone">+44 (0) 2380 248248</span></div>
                    <div>Fax: <span itemprop="faxNumber">(023) 80 248249</span></div>
                    <div style="display: none;">Url: <span itemprop="url">https://www.bon-voyage.co.uk/</span></div>
                    <meta itemprop="openingHours"  style="display: none"  datetime="Mo,Tu,We,Th 09:00-18:00 Fr 09:00-17:30 Sa 9:00-15:00" />
                    <div itemtype="http://schema.org/PostalAddress" itemscope="" itemprop="address">
                        <div itemprop="streetAddress">16-18 Bellevue Road</div>
                        <div><span itemprop="addressLocality">Southampton</span>, <span itemprop="addressRegion">Hampshire</span> <span itemprop="postalCode">SO15 2AY</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    </div>

                    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
            window.loadOtherJs = () => {
                loadJS("https://www.bon-voyage.co.uk/js/build/site.js", function() {
                    $$('body')[0].insert("<script type=\"text\/javascript\">\n\/\/<![CDATA[\nnew App.sectionTabs($$('.js-related-tabs').first(), $$('.js-related-content').first());\n\/\/]]>\n<\/script>\n\t<script type=\"text\/javascript\">\n\/\/<![CDATA[\nvar App = window.App||{}; App.gmaps = App.gmaps||[]; App.gmaps.push(function() {var map = new App.Map('image-and-map__map', {\"id\":\"475\",\"address\":\"British Columbia -  Off the Beaten Track\",\"map_latitude\":\"52.049583\",\"map_longitude\":\"-126.207977\",\"map_type\":\"roadmap\",\"zoom_level\":\"5\"});map.addPath([{\"id\":\"4028\",\"address\":\"Vancouver\",\"latitude\":\"49.257736\",\"longitude\":\"-123.123901\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4029\",\"address\":\"70 Mile House\",\"latitude\":\"52.149265\",\"longitude\":\"-122.112335\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4030\",\"address\":\"Prince George\",\"latitude\":\"53.926899\",\"longitude\":\"-122.752419\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4031\",\"address\":\"Smithers\",\"latitude\":\"54.791039\",\"longitude\":\"-127.159195\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4032\",\"address\":\"Terrace\",\"latitude\":\"54.498562\",\"longitude\":\"-128.542679\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4033\",\"address\":\"Prince Rupert\",\"latitude\":\"54.266003\",\"longitude\":\"-130.303619\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4034\",\"address\":\"Port Hardy \",\"latitude\":\"50.663288\",\"longitude\":\"-127.424881\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4035\",\"address\":\"Campbell River\",\"latitude\":\"50.010944\",\"longitude\":\"-125.301064\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4036\",\"address\":\"Tofini\",\"latitude\":\"49.128983\",\"longitude\":\"-125.886314\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4037\",\"address\":\"Parksville\",\"latitude\":\"49.322636\",\"longitude\":\"-124.301956\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4038\",\"address\":\"Vancouver\",\"latitude\":\"49.257736\",\"longitude\":\"-123.123901\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"},{\"id\":\"4039\",\"address\":\"Vancouver\",\"latitude\":\"49.257736\",\"longitude\":\"-123.123901\",\"controller\":\"itinerary_days\",\"model\":\"ItineraryDay\"}]);});\n\/\/]]>\n<\/script>");
                });

                loadJS('https://assets.bon-voyage.co.uk' + '/bower_components/modernizr/modernizr_build.js');
            };

            loadJS("/js/build/components.js?ver=1725289803");
        </script>
    
        <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Q98XWBFEC9"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'G-Q98XWBFEC9');
    </script>
    <!-- END GTAG -->
    <noscript>
        <div style="display:inline;">
            <img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/1030719956/?value=0&amp;guid=ON&amp;script=0" />
        </div>
        <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=929810233762201&ev=PageView&noscript=1" />

        <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N7J7423" height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>

    <script type="text/javascript">
        var $zoho = $zoho || {};
        $zoho.salesiq = $zoho.salesiq || {
            widgetcode: 'd95938ffe2a6832100e58db2a757808689e3d8ff12421455da2686b83d2140f792bfd2045cbc83c765e85ec0ea2b18a2',
            values: {},
            ready: function() {},
        };
        var d = document;
        s = d.createElement('script');
        s.type = 'text/javascript';
        s.id = 'zsiqscript';
        s.defer = true;
        s.src = 'https://salesiq.zoho.eu/widget';
        t = d.getElementsByTagName('script')[0];
        t.parentNode.insertBefore(s, t);
        d.write("<div id='zsiqwidget'></div>");
    </script>
    <script type="text/javascript">
        (function(a, e, c, f, g, h, b, d) {
            var k = {
                ak: "1030719956",
                cl: "g2wPCMnNj3sQ1JO-6wM",
                autoreplace: "0800 316 3012"
            };
            a[c] = a[c] || function() {
                (a[c].q = a[c].q || []).push(arguments)
            };
            a[g] || (a[g] = k.ak);
            b = e.createElement(h);
            b.async = 1;
            b.src = "//www.gstatic.com/wcm/loader.js";
            d = e.getElementsByTagName(h)[0];
            d.parentNode.insertBefore(b, d);
            a[f] = function(b, d, e) {
                a[c](2, b, k, d, null, new Date, e)
            };
            a[f]()
        })(window, document, "_googWcmImpl", "_googWcmGet", "_googWcmAk", "script");
    </script>
    <script async src="https://www.google.com/recaptcha/api.js?render=6LenjsgZAAAAAH-f4aj1WPD8Rflsj57FOaAcvtkU"></script>
    <script src="/js/tweaks.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.js"></script>
    <script src="/js/navigation.js"></script>
    <script src="/js/search-toggle-fix.js"></script>
    <!-- Updated -->

            <div class="page-wrapper__contact-module mobile-bar">
            <div class="page-wrapper__contact-module-inner">
                <div class="call-us-module">

<div class="nav-bar nav-medium-up">

        <p id="nav-medium-call">
            <a class="quote-module__request" href="tel:08003163012"><b>0800 316 3012</b></a>        </p>

        <p id="nav-medium-email">
            <a href="/cdn-cgi/l/email-protection#c5b6a4a9a0b685a7aaabe8b3aabca4a2a0eba6aaebb0ae" class="quote-module__request">Email an Expert <b>Now</b></a>        </p>

        <p id="nav-medium-enquire">
            <a href="/make_an_enquiry" class="quote-module__request">Make an <b>Enquiry</b></a>        </p>

        <p id="nav-medium-newsletter">
            <a href="/subscriptions" class="quote-module__request">Newsletter <b>sign up</b></a>        </p>
</div>
<div class="nav-bar nav-small">
    <ul>
        <li id="nav-small-call"><a href="tel:08003163012"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M28.8646 21.8218L24.34 21.3052C23.8079 21.2427 23.2687 21.3016 22.7627 21.4775C22.2567 21.6534 21.7971 21.9416 21.4185 22.3206L18.1408 25.5983C13.0842 23.0259 8.97404 18.9157 6.40164 13.8591L9.69716 10.5635C10.4631 9.79755 10.8372 8.72873 10.7125 7.64209L10.1959 3.15303C10.0953 2.28388 9.67839 1.48215 9.02463 0.900654C8.37086 0.319161 7.52598 -0.00142522 6.65103 4.76331e-06H3.56927C1.55633 4.76331e-06 -0.118152 1.67449 0.0065438 3.68745C0.950666 18.9004 13.1174 31.0493 28.3124 31.9935C30.3253 32.1182 31.9998 30.4437 31.9998 28.4307V25.3489C32.0176 23.5498 30.6638 22.0356 28.8646 21.8218Z" fill="currentColor"/>
</svg>
<span>Call</span></a></li>
        <li id="nav-small-enquire"><a href="/make_an_enquiry"><svg width="31" height="33" viewBox="0 0 31 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.1 31.4286C2.2475 31.4286 1.51797 31.1211 0.9114 30.5061C0.304833 29.8912 0.00103333 29.151 0 28.2857V6.28572C0 5.42143 0.3038 4.68181 0.9114 4.06686C1.519 3.45191 2.24853 3.14391 3.1 3.14286H4.65V1.57143C4.65 1.1262 4.7988 0.753244 5.0964 0.452577C5.394 0.15191 5.76187 0.00105304 6.2 5.41871e-06C6.63813 -0.0010422 7.00651 0.149815 7.30515 0.452577C7.60378 0.755339 7.75206 1.12829 7.75 1.57143V3.14286H20.15V1.57143C20.15 1.1262 20.2988 0.753244 20.5964 0.452577C20.894 0.15191 21.2619 0.00105304 21.7 5.41871e-06C22.1381 -0.0010422 22.5065 0.149815 22.8051 0.452577C23.1038 0.755339 23.2521 1.12829 23.25 1.57143V3.14286H24.8C25.6525 3.14286 26.3825 3.45086 26.9901 4.06686C27.5977 4.68286 27.901 5.42248 27.9 6.28572V13.6321C27.9 14.0774 27.7512 14.4509 27.4536 14.7526C27.156 15.0543 26.7881 15.2046 26.35 15.2036C25.9119 15.2025 25.544 15.0517 25.2464 14.751C24.9488 14.4503 24.8 14.0774 24.8 13.6321V12.5714H3.1V28.2857H12.09C12.5292 28.2857 12.8975 28.4366 13.1951 28.7383C13.4927 29.04 13.641 29.413 13.64 29.8571C13.639 30.3013 13.4902 30.6748 13.1936 30.9776C12.897 31.2803 12.5292 31.4307 12.09 31.4286H3.1ZM23.25 33C21.1058 33 19.2784 32.2337 17.7676 30.701C16.2569 29.1683 15.501 27.3156 15.5 25.1429C15.499 22.9701 16.2548 21.1174 17.7676 19.5847C19.2804 18.052 21.1079 17.2857 23.25 17.2857C25.3921 17.2857 27.2201 18.052 28.7339 19.5847C30.2477 21.1174 31.0031 22.9701 31 25.1429C30.9969 27.3156 30.241 29.1689 28.7323 30.7026C27.2237 32.2363 25.3962 33.0021 23.25 33ZM24.025 24.8286V21.2143C24.025 21.0048 23.9475 20.8214 23.7925 20.6643C23.6375 20.5071 23.4567 20.4286 23.25 20.4286C23.0433 20.4286 22.8625 20.5071 22.7075 20.6643C22.5525 20.8214 22.475 21.0048 22.475 21.2143V24.7893C22.475 24.9988 22.5137 25.202 22.5912 25.399C22.6687 25.596 22.785 25.7725 22.94 25.9286L25.3037 28.325C25.4587 28.4821 25.6396 28.5607 25.8462 28.5607C26.0529 28.5607 26.2337 28.4821 26.3887 28.325C26.5437 28.1679 26.6212 27.9845 26.6212 27.775C26.6212 27.5655 26.5437 27.3821 26.3887 27.225L24.025 24.8286Z" fill="currentColor"/>
</svg>
<span>Enquire</span></a></li>
        <li id="nav-small-chat" class="disabled"><a href="#"><svg width="34" height="33" viewBox="0 0 34 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M34 10.56C34 9.85983 33.7244 9.18834 33.234 8.69324C32.7435 8.19814 32.0783 7.92 31.3846 7.92H26.1538V2.64C26.1538 1.93983 25.8783 1.26833 25.3878 0.773238C24.8973 0.278142 24.2321 0 23.5385 0H2.61538C1.92174 0 1.25651 0.278142 0.766028 0.773238C0.275549 1.26833 0 1.93983 0 2.64V23.76C0.0007678 24.0083 0.0709214 24.2514 0.202401 24.4613C0.333881 24.6712 0.521354 24.8394 0.743282 24.9466C0.96521 25.0538 1.21259 25.0956 1.457 25.0672C1.70142 25.0389 1.93295 24.9415 2.125 24.7863L7.84615 20.13V25.08C7.84615 25.7802 8.1217 26.4517 8.61218 26.9468C9.10266 27.4419 9.76789 27.72 10.4615 27.72H25.7599L31.875 32.7063C32.1064 32.8952 32.3947 32.9988 32.6923 33C33.0391 33 33.3717 32.8609 33.617 32.6134C33.8622 32.3658 34 32.0301 34 31.68V10.56ZM27.0447 25.3737C26.8133 25.1848 26.525 25.0812 26.2274 25.08H10.4615V19.8H23.5385C24.2321 19.8 24.8973 19.5219 25.3878 19.0268C25.8783 18.5317 26.1538 17.8602 26.1538 17.16V10.56H31.3846V28.9163L27.0447 25.3737Z" fill="currentColor"/>
</svg>
<span>Chat</span></a></li>
    </ul>
</div>
</div>
<script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
document.addEventListener("DOMContentLoaded", function () {

    var chatButton = document.querySelector('#nav-small-chat');

    $zoho.salesiq.ready = function() {

        // $zoho.salesiq.chat.online(function()
        // {
            chatButton.classList.remove('disabled');
        // });

    }

    chatButton.addEventListener('click', function(e) {
        e.preventDefault();
        $zoho.salesiq.chat.start();
    });

    /* SCROLL FUNCTIONS */

    var mobileBarElement = document.querySelector('.mobile-bar');
    window.mobileBar = window.mobileBar || {};
    if (mobileBarElement !== null) {
        var mobileBarDisplay = window.getComputedStyle(mobileBarElement).display;
        window.mobileBar.status = (mobileBarDisplay == 'block' || mobileBarDisplay == 'flex') ? 'open' : 'closed';
    }

    /* Hide header on scroll down, show on scroll up */
    var didScroll,
        lastScrollTop = 0,
        delta = 5,
        windowInnerHeight = window.innerHeight,
        mobileBarHeight = mobileBarElement ? mobileBarElement.offsetHeight : 0;

    window.addEventListener('scroll', function () {
        didScroll = true;
    });

    setInterval(function () {
        if (didScroll) {
            hasScrolled();
            didScroll = false;
        }
    }, 250);

    function hasScrolled() {
        var st = window.scrollY;

        if (st > lastScrollTop) {
            // Scroll Down
            // Show bar
            if (mobileBarElement && !mobileBarElement.classList.contains('mobile-bar-show')) {
                    mobileBarElement.classList.add('mobile-bar-show');
                    mobileBarElement.classList.remove('mobile-bar-hide');
                    const showEvent = new Event('mobilebar:show');
                    window.dispatchEvent(showEvent);
                    window.mobileBar.status = 'open';
            }
        } else {
            // Scroll Up
            mobileBarElement.classList.remove('mobile-bar-show');
            mobileBarElement.classList.add('mobile-bar-hide');
            const hideEvent = new Event('mobilebar:hide');
            window.dispatchEvent(hideEvent);
            window.mobileBar.status = 'closed';
        }

        if (Math.abs(lastScrollTop - st) <= delta) {
            return;
        }

        lastScrollTop = st;
    }
});
</script>
            </div>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
          var mobileBar = document.querySelector('.page-wrapper__contact-module.mobile-bar');
          if (mobileBar && mobileBar.parentNode !== document.body) {
            document.body.appendChild(mobileBar);
            console.log('[MOBILE-BAR] Moved to body');
          }
        });
        </script>
    </body>

</html>
