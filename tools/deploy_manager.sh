#!/bin/bash

# Here's a dynamic Bash script that achieves your requirements. It supports --files and --mode options, dynamically handles files, and provides clear logging.

# Script: deploy_manager.sh

# Get the current working directory (where the command is run from)
WORKING_DIR="$(pwd)"

# Default docroot path
DEFAULT_DOCROOT="/var/app/current"

# Directory for backup files (in the working directory)
BACKUP_DIR="$WORKING_DIR/_archive"

# Ensure the backup directory exists
mkdir -p "$BACKUP_DIR"

# Function to display usage
usage() {
    echo
    echo -e "\033[1mUsage:\033[0m $0 --files \"file1 file2 ...\" --mode deploy|rollback [--docroot path] [--dry-run] [--force] [--no-cache-clear]"
    echo
    echo "  -f, --files        Space-separated list of file paths to process"
    echo "  -m, --mode         Mode to run: deploy or rollback"
    echo "  -d, --docroot      Target document root (default: /var/app/current)"
    echo "  -n, --dry-run      Show what would happen without making changes"
    echo "  --force            Continue even if ownership change fails"
    echo "  --no-cache-clear   Skip clearing the application cache after deployment"
    echo ""
    echo -e "\033[1mExamples:\033[0m"
    echo
    echo "  # Preview deployment of a file without making changes:"
    echo "  $0 --files \"app/views/layouts/prod/default.ctp\" --mode deploy --dry-run"
    echo ""
    exit 1
}

# Parse arguments
FILES=()
MODE=""
DOCROOT="$DEFAULT_DOCROOT"
DRY_RUN=false
FORCE=false
CLEAR_CACHE=true
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--files)
            IFS=' ' read -r -a FILES <<< "$2"
            shift 2
            ;;
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -d|--docroot)
            DOCROOT="$2"
            shift 2
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --no-cache-clear)
            CLEAR_CACHE=false
            shift
            ;;
        *)
            usage
            ;;
    esac
done

# Validate arguments
if [[ -z "$FILES" || -z "$MODE" ]]; then
    usage
fi

if [[ "$MODE" != "deploy" && "$MODE" != "rollback" ]]; then
    echo "Error: Invalid mode '$MODE'. Use 'deploy' or 'rollback'."
    exit 1
fi

# Expand docroot path (handle ~/ and other expansions)
DOCROOT=$(eval echo "$DOCROOT")

# Print operation header with reverse video
MODE_DISPLAY="$(tr '[:lower:]' '[:upper:]' <<< ${MODE:0:1})${MODE:1}"
if [ "$DRY_RUN" = true ]; then
    printf "\nDRY RUN: %sing...\n\n" "$MODE_DISPLAY"
else
    printf "\n%sing...\n\n" "$MODE_DISPLAY"
fi

# Get the list of files in the working directory
SCRIPT_NAME=$(basename "$0")
AVAILABLE_FILES=$(ls -p "$WORKING_DIR" | grep -v "/$")

# Get total number of files
TOTAL_FILES=${#FILES[@]}
CURRENT_FILE=1

# Perform operations on each file
for FILE in "${FILES[@]}"; do
    BASENAME=$(basename "$FILE")

    # Check if source file exists in working directory
    if ! [[ "$AVAILABLE_FILES" =~ "$BASENAME" ]]; then
        printf "[%d/%d] ❌ %s - Source file not found\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
        ((CURRENT_FILE++))
        continue
    fi

    # Change ownership of the local file before any operations (silently)
    if [ "$DRY_RUN" = false ]; then
        if ! sudo chown webapp:webapp "$WORKING_DIR/$BASENAME" 2>/dev/null; then
            if [ "$FORCE" = false ]; then
                printf "[%d/%d] ❌ %s - Failed to change ownership\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                echo "Error: Failed to change ownership. Please check if group 'webapp' exists."
                echo "Use --force to continue anyway."
                exit 1
            fi
        fi
    fi

    case $MODE in
        deploy)
            # Check if destination file exists and handle accordingly
            if [ -f "$DOCROOT/$FILE" ]; then
                # Backup the existing file
                if [ "$DRY_RUN" = false ]; then
                    if sudo cp "$DOCROOT/$FILE" "$BACKUP_DIR/$BASENAME" 2>/dev/null; then
                        # Deploy the new file
                        if sudo cp "$WORKING_DIR/$BASENAME" "$DOCROOT/$FILE" 2>/dev/null; then
                            printf "[%d/%d] ✅ %s\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                        else
                            printf "[%d/%d] ❌ %s - Failed to deploy\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                        fi
                    else
                        printf "[%d/%d] ❌ %s - Failed to create backup\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    fi
                else
                    printf "[%d/%d] ✅ %s (DRY RUN)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                fi
            else
                # No existing file - deploy new file
                if [ "$DRY_RUN" = false ]; then
                    # Ensure target directory exists
                    TARGET_DIR=$(dirname "$DOCROOT/$FILE")
                    if sudo mkdir -p "$TARGET_DIR" 2>/dev/null && sudo cp "$WORKING_DIR/$BASENAME" "$DOCROOT/$FILE" 2>/dev/null; then
                        printf "[%d/%d] ✅ %s - No existing file\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    else
                        printf "[%d/%d] ❌ %s - Failed to deploy\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    fi
                else
                    printf "[%d/%d] ✅ %s - No existing file (DRY RUN)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                fi
            fi
            ;;
        rollback)
            # Rollback to the backup file
            if [[ -f "$BACKUP_DIR/$BASENAME" ]]; then
                if [ "$DRY_RUN" = false ]; then
                    if sudo cp "$BACKUP_DIR/$BASENAME" "$DOCROOT/$FILE" 2>/dev/null; then
                        printf "[%d/%d] ✅ %s\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    else
                        printf "[%d/%d] ❌ %s - Failed to rollback\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                    fi
                else
                    printf "[%d/%d] ✅ %s (DRY RUN)\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
                fi
            else
                printf "[%d/%d] ❌ %s - No backup found\n" "$CURRENT_FILE" "$TOTAL_FILES" "$FILE"
            fi
            ;;
    esac

    ((CURRENT_FILE++))
done

# Clear cache after deployment if enabled
if { [ "$MODE" = "deploy" ] || [ "$MODE" = "rollback" ]; } && [ "$CLEAR_CACHE" = true ]; then
    if [ "$DRY_RUN" = false ]; then
        printf "\nClearing application cache...\n"
        if sudo find "$DOCROOT/app/tmp/cache/" -type f -delete 2>/dev/null; then
            echo "✅ Cache cleared successfully."
        else
            echo "❌ Failed to clear cache."
        fi
    else
        printf "\nDRY RUN: Would clear application cache\n"
        echo "✅ Would run: sudo find $DOCROOT/app/tmp/cache/ -type f -delete"
    fi
    echo
fi

echo "Operation '$MODE' complete"
echo

# How to Use:

# 	1.	Navigate to the folder containing the files to deploy
# 	2.	Run the script (from any location) with the required options
# 	•	For deployment:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp app/webroot/js/tweaks.js" --mode deploy


# 	•	For rollback:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp app/webroot/js/tweaks.js" --mode rollback


# 	•	To deploy without clearing cache:

# /path/to/deploy_manager.sh --files "app/views/layouts/prod/default.ctp" --mode deploy --no-cache-clear


# Key Features:

# 	1.	Location Independent: Script can be located anywhere, operates on files in current working directory
# 	2.	Dynamic File Handling: Matches filenames from the provided --files argument with files in working directory
# 	3.	Error Handling: Skips files not present in the working directory or missing backups
# 	4.	Mode Validation: Ensures valid modes (deploy or rollback) are used
# 	5.	Backup Management: Creates backups in ./_archive (in working directory) during deployment
# 	6.	Clear Logs: Prints actions for transparency with full paths
# 	7.	Cache Clearing: Automatically clears application cache after deployment (can be disabled)
#
# This script adapts to your specific requirements and ensures safe, repeatable file deployments and rollbacks.
